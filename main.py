#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API数据收集器主程序
支持并发执行所有API请求器，实现高效的批量数据刷新
"""

import os
import sys
import time
import json
import glob
import logging
import logging.handlers
import argparse
import multiprocessing
import requests
from pathlib import Path
from config import get_data_storage_path
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Tuple
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import (
    ensure_data_storage_directories,
    DATA_STORAGE_CONFIG,
    SCHEDULER_CONFIG,
    API_REQUESTER_INTERVALS,
    LOG_FORMAT,
    LOG_LEVEL,
    USER_AGENT,
    HTTP_SESSION_CONFIG
)

# ========== 集成的工具类 ==========

def setup_logger(name: str = __name__,
                level: str = LOG_LEVEL,
                log_to_file: bool = True,
                log_dir: str = "logs") -> logging.Logger:
    """
    设置日志器

    Args:
        name: 日志器名称
        level: 日志级别
        log_to_file: 是否输出到文件
        log_dir: 日志文件目录

    Returns:
        配置好的日志器
    """
    # 创建日志器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    # 创建格式器
    formatter = logging.Formatter(LOG_FORMAT)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(getattr(logging, level.upper()))
    logger.addHandler(console_handler)

    # 文件处理器（如果启用）
    if log_to_file:
        # 确保日志目录存在
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)

        # 按日期轮转的文件处理器
        log_filename = log_path / f"api_data_collector_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_filename,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=7,          # 保留7个备份
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(getattr(logging, level.upper()))
        logger.addHandler(file_handler)

    return logger

# 配置全局日志
logger = setup_logger("main")

class FileManager:
    """文件管理器 - 处理所有数据存储相关操作"""

    def __init__(self):
        """初始化文件管理器"""
        self.storage_root = DATA_STORAGE_CONFIG["root_dir"]
        self.cache_ttl = DATA_STORAGE_CONFIG["cache_ttl_seconds"]
        self.max_files_per_dir = DATA_STORAGE_CONFIG["max_files_per_dir"]
        self.cleanup_older_than_days = DATA_STORAGE_CONFIG["cleanup_older_than_days"]
        self.file_naming_pattern = DATA_STORAGE_CONFIG["file_naming_pattern"]
        self.enable_cleanup = DATA_STORAGE_CONFIG.get("enable_cleanup", True)
        self.show_file_stats = DATA_STORAGE_CONFIG.get("show_file_stats", False)

        # 确保存储根目录存在
        self.storage_root.mkdir(parents=True, exist_ok=True)

        cleanup_status = "禁用" if not self.enable_cleanup else "启用"
        logger.info(f"✅ 文件管理器初始化完成，存储目录: {self.storage_root}")
        logger.info(f"🗂️ 清理功能: {cleanup_status}, 文件统计: {'启用' if self.show_file_stats else '禁用'}")

    def save_api_data(self, api_identifier: str, data: Dict[str, Any],
                     metadata: Optional[Dict[str, Any]] = None) -> Tuple[bool, str, str]:
        """
        保存API数据到对应的目录（支持按API来源分组的新结构）

        Args:
            api_identifier: API标识符，用作目录名（如 'binance_get_futures_exchange_info'）
            data: 要保存的数据
            metadata: 可选的元数据

        Returns:
            (成功标志, 文件路径, 状态信息)
        """
        try:
            # 根据API标识符确定API来源和目录结构
            if api_identifier.startswith('binance_'):
                provider = 'binance'
            elif api_identifier.startswith('coinglass_'):
                provider = 'coinglass'
            elif api_identifier.startswith('google_ai_'):
                provider = 'google_ai'
            else:
                provider = 'other'

            # 创建API专用目录（按来源分组）
            api_dir = get_data_storage_path(api_identifier)
            api_dir.mkdir(parents=True, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.file_naming_pattern.format(
                timestamp=timestamp,
                api_name=api_identifier
            )

            file_path = api_dir / filename

            # 构建完整的数据结构
            full_data = {
                "timestamp": timestamp,
                "api_identifier": api_identifier,
                "data": data,
                "metadata": metadata or {}
            }

            # 保存数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(full_data, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ API数据已保存: {file_path}")

            # 显示文件统计信息
            if self.show_file_stats:
                self._show_file_stats(api_dir, api_identifier)

            # 执行清理（如果启用）
            if self.enable_cleanup:
                self._cleanup_old_files(api_dir)
            else:
                logger.info("🗂️ 自动清理已禁用，文件将永久保留")

            return True, str(file_path), f"数据已保存到 {filename}"

        except Exception as e:
            error_msg = f"保存API数据失败: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def load_latest_api_data(self, api_identifier: str) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        加载指定API的最新数据

        Args:
            api_identifier: API标识符

        Returns:
            (成功标志, 数据, 状态信息)
        """
        try:
            # 根据API标识符确定API来源和目录结构
            if api_identifier.startswith('binance_'):
                provider = 'binance'
            elif api_identifier.startswith('coinglass_'):
                provider = 'coinglass'
            elif api_identifier.startswith('google_ai_'):
                provider = 'google_ai'
            else:
                provider = 'other'

            api_dir = get_data_storage_path(api_identifier)
            if not api_dir.exists():
                return False, None, f"API目录不存在: {provider}/{api_identifier}"

            # 查找最新的数据文件
            pattern = f"*_{api_identifier}.json"
            files = list(api_dir.glob(pattern))

            if not files:
                return False, None, f"未找到数据文件: {pattern}"

            # 按修改时间排序，获取最新文件
            latest_file = max(files, key=lambda f: f.stat().st_mtime)

            # 读取数据
            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            logger.info(f"✅ 已加载最新数据: {latest_file.name}")
            return True, data, f"已加载 {latest_file.name}"

        except Exception as e:
            error_msg = f"加载API数据失败: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg

    def is_data_fresh(self, api_identifier: str, max_age_seconds: Optional[int] = None) -> bool:
        """
        检查数据是否新鲜

        Args:
            api_identifier: API标识符
            max_age_seconds: 最大年龄（秒），None则使用默认缓存TTL

        Returns:
            数据是否新鲜
        """
        try:
            max_age = max_age_seconds or self.cache_ttl

            # 根据API标识符确定API来源和目录结构
            if api_identifier.startswith('binance_'):
                provider = 'binance'
            elif api_identifier.startswith('coinglass_'):
                provider = 'coinglass'
            elif api_identifier.startswith('google_ai_'):
                provider = 'google_ai'
            else:
                provider = 'other'

            api_dir = get_data_storage_path(api_identifier)

            if not api_dir.exists():
                return False

            # 查找最新的数据文件
            pattern = f"*_{api_identifier}.json"
            files = list(api_dir.glob(pattern))

            if not files:
                return False

            # 获取最新文件的修改时间
            latest_file = max(files, key=lambda f: f.stat().st_mtime)
            file_age = time.time() - latest_file.stat().st_mtime

            return file_age <= max_age

        except Exception as e:
            logger.error(f"检查数据新鲜度失败: {str(e)}")
            return False

    def _show_file_stats(self, api_dir: Path, api_identifier: str):
        """显示文件统计信息"""
        try:
            # 获取所有JSON文件
            files = list(api_dir.glob("*.json"))

            if not files:
                logger.info(f"📊 {api_identifier}: 暂无数据文件")
                return

            # 计算总大小
            total_size = sum(f.stat().st_size for f in files)
            total_size_mb = total_size / (1024 * 1024)

            # 获取最新和最旧文件的时间
            files_with_time = [(f, f.stat().st_mtime) for f in files]
            files_with_time.sort(key=lambda x: x[1])

            oldest_file = files_with_time[0][0]
            newest_file = files_with_time[-1][0]

            oldest_time = datetime.fromtimestamp(files_with_time[0][1]).strftime("%Y-%m-%d %H:%M:%S")
            newest_time = datetime.fromtimestamp(files_with_time[-1][1]).strftime("%Y-%m-%d %H:%M:%S")

            logger.info(f"📊 {api_identifier} 文件统计:")
            logger.info(f"   📁 文件数量: {len(files)} 个")
            logger.info(f"   💾 总大小: {total_size_mb:.2f} MB")
            logger.info(f"   📅 最旧文件: {oldest_file.name} ({oldest_time})")
            logger.info(f"   🆕 最新文件: {newest_file.name} ({newest_time})")

        except Exception as e:
            logger.error(f"显示文件统计失败: {str(e)}")

    def _cleanup_old_files(self, api_dir: Path):
        """清理旧文件（仅在启用清理时执行）"""
        try:
            # 检查是否启用清理
            if not self.enable_cleanup:
                return

            # 获取所有JSON文件
            files = list(api_dir.glob("*.json"))

            deleted_count = 0

            # 如果文件数量超过限制，删除最旧的文件
            if self.max_files_per_dir > 0 and len(files) > self.max_files_per_dir:
                # 按修改时间排序
                files.sort(key=lambda f: f.stat().st_mtime)

                # 删除最旧的文件
                files_to_delete = files[:-self.max_files_per_dir]
                for file_to_delete in files_to_delete:
                    file_to_delete.unlink()
                    deleted_count += 1
                    logger.info(f"🗑️ 已删除旧文件: {file_to_delete.name}")

            # 删除超过指定天数的文件
            if self.cleanup_older_than_days > 0:
                cutoff_time = time.time() - (self.cleanup_older_than_days * 24 * 3600)
                for file_path in files:
                    if file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        deleted_count += 1
                        logger.info(f"🗑️ 已删除过期文件: {file_path.name}")

            if deleted_count == 0:
                logger.info("🗂️ 无需清理文件")

        except Exception as e:
            logger.error(f"清理文件失败: {str(e)}")

class HTTPClient:
    """HTTP客户端 - 提供统一的HTTP请求功能"""

    def __init__(self, timeout: int = 30, max_retries: int = 3):
        """
        初始化HTTP客户端

        Args:
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
        """
        self.timeout = timeout
        self.max_retries = max_retries

        # 创建会话
        self.session = requests.Session()

        # 配置连接池和重试策略
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        adapter = HTTPAdapter(
            pool_connections=HTTP_SESSION_CONFIG["pool_connections"],
            pool_maxsize=HTTP_SESSION_CONFIG["pool_maxsize"],
            max_retries=retry_strategy,
            pool_block=HTTP_SESSION_CONFIG["pool_block"]
        )

        self.session.mount('https://', adapter)
        self.session.mount('http://', adapter)

        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': USER_AGENT,
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })

        logger.info(f"✅ HTTP客户端初始化完成，超时: {timeout}s, 重试: {max_retries}次")

    def add_auth_header(self, key: str, value: str):
        """添加认证头"""
        self.session.headers[key] = value
        logger.info(f"✅ 已添加认证头: {key}")

    def get(self, url: str, params: Optional[Dict] = None,
            headers: Optional[Dict] = None, **kwargs) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        发送GET请求

        Args:
            url: 请求URL
            params: 查询参数
            headers: 请求头
            **kwargs: 其他参数

        Returns:
            (成功标志, 响应数据, 状态信息)
        """
        return self._request('GET', url, params=params, headers=headers, **kwargs)

    def post(self, url: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None,
             headers: Optional[Dict] = None, **kwargs) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        发送POST请求

        Args:
            url: 请求URL
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            **kwargs: 其他参数

        Returns:
            (成功标志, 响应数据, 状态信息)
        """
        return self._request('POST', url, data=data, json=json_data, headers=headers, **kwargs)

    def _request(self, method: str, url: str, **kwargs) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        内部请求方法

        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他参数

        Returns:
            (成功标志, 响应数据, 状态信息)
        """
        for attempt in range(self.max_retries + 1):
            try:
                # 合并请求头
                headers = kwargs.pop('headers', {})
                if headers:
                    request_headers = self.session.headers.copy()
                    request_headers.update(headers)
                    kwargs['headers'] = request_headers

                # 移除可能传递的不支持参数
                kwargs.pop('max_retries', None)

                # 设置超时
                kwargs.setdefault('timeout', self.timeout)

                # 发送请求
                response = self.session.request(method, url, **kwargs)

                # 检查状态码
                response.raise_for_status()

                # 尝试解析JSON
                try:
                    data = response.json()
                    return True, data, f"请求成功 (状态码: {response.status_code})"
                except ValueError:
                    logger.warning(f"响应不是有效的JSON格式: {url}")
                    return True, {"raw_content": response.text}, f"请求成功，但响应非JSON格式"

            except requests.exceptions.RequestException as e:
                if attempt < self.max_retries:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"请求失败，{wait_time}秒后重试 ({attempt + 1}/{self.max_retries + 1}): {str(e)}")
                    time.sleep(wait_time)
                else:
                    logger.error(f"请求最终失败: {str(e)}")
                    return False, None, f"请求失败: {str(e)}"
            except Exception as e:
                logger.error(f"请求异常: {str(e)}")
                return False, None, f"请求异常: {str(e)}"

        return False, None, "请求失败"

    def close(self):
        """关闭HTTP会话"""
        try:
            self.session.close()
            logger.info("✅ HTTP会话已关闭")
        except Exception as e:
            logger.error(f"关闭HTTP会话失败: {str(e)}")

class APIScheduler:
    """API调度器 - 支持独立间隔管理的定时执行API数据收集"""

    def __init__(self, collector: 'APIDataCollector'):
        """
        初始化调度器

        Args:
            collector: API数据收集器实例
        """
        self.collector = collector
        self.global_interval = SCHEDULER_CONFIG["interval_seconds"]
        self.check_interval = SCHEDULER_CONFIG["check_interval"]
        self.run_on_startup = SCHEDULER_CONFIG["run_on_startup"]
        self.use_individual_intervals = SCHEDULER_CONFIG["use_individual_intervals"]
        self.max_concurrent_jobs = SCHEDULER_CONFIG["max_concurrent_jobs"]
        self.retry_failed_jobs = SCHEDULER_CONFIG["retry_failed_jobs"]

        # 运行状态
        self.is_running = False
        self.current_job_count = 0
        self.start_time = None

        # 统计信息
        self.total_runs = 0
        self.successful_runs = 0
        self.failed_runs = 0
        self.requester_stats = {}  # 每个请求器的统计信息

        # 独立间隔管理
        self.requester_intervals = API_REQUESTER_INTERVALS.copy()
        self.last_run_times = {}  # 记录每个请求器的最后执行时间

        # 初始化统计信息
        for requester_name in self.collector.api_requesters.keys():
            self.requester_stats[requester_name] = {
                "total_runs": 0,
                "successful_runs": 0,
                "failed_runs": 0,
                "last_run_time": None,
                "next_run_time": None
            }
            self.last_run_times[requester_name] = 0

        logger.info(f"✅ 调度器初始化完成")
        logger.info(f"   独立间隔管理: {'启用' if self.use_individual_intervals else '禁用'}")
        logger.info(f"   全局间隔: {self.global_interval}秒")
        logger.info(f"   检查间隔: {self.check_interval}秒")
        logger.info(f"   启动时执行: {'是' if self.run_on_startup else '否'}")
        logger.info(f"   最大并发: {self.max_concurrent_jobs}")

        if self.use_individual_intervals:
            logger.info(f"📋 各请求器独立间隔配置:")
            for name, interval in self.requester_intervals.items():
                if name in self.collector.api_requesters:
                    logger.info(f"   {name}: {interval}秒 ({interval//60}分钟)" if interval >= 60 else f"   {name}: {interval}秒")

    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("⚠️ 调度器已在运行中")
            return

        self.is_running = True
        self.start_time = datetime.now()

        if self.use_individual_intervals:
            logger.info("🚀 启动独立间隔管理调度器...")
            logger.info(f"⏰ 检查间隔: {self.check_interval}秒")
        else:
            logger.info("🚀 启动全局间隔调度器...")
            logger.info(f"⏰ 执行间隔: {self.global_interval}秒")

        logger.info("🛑 按 Ctrl+C 停止调度器")

        try:
            # 启动时立即执行一次（如果配置启用）
            if self.run_on_startup:
                logger.info("🎯 启动时立即执行一次...")
                if self.use_individual_intervals:
                    self._execute_individual_jobs()
                else:
                    self._execute_all_jobs()

            # 开始定时循环
            while self.is_running:
                if self.use_individual_intervals:
                    time.sleep(self.check_interval)
                    if self.is_running:
                        self._check_and_execute_jobs()
                else:
                    time.sleep(self.global_interval)
                    if self.is_running:
                        self._execute_all_jobs()

        except KeyboardInterrupt:
            logger.info("⚡ 接收到停止信号")
        except Exception as e:
            logger.error(f"❌ 调度器异常: {str(e)}")
        finally:
            self.stop()

    def stop(self):
        """停止调度器"""
        if not self.is_running:
            return

        self.is_running = False
        end_time = datetime.now()
        total_time = end_time - self.start_time if self.start_time else timedelta(0)

        logger.info("🛑 调度器已停止")
        logger.info(f"📊 总体运行统计:")
        logger.info(f"   总运行时间: {total_time}")

        if self.use_individual_intervals:
            # 显示各请求器的详细统计
            logger.info(f"📋 各请求器统计:")
            for requester_name, stats in self.requester_stats.items():
                if stats["total_runs"] > 0:
                    success_rate = (stats["successful_runs"] / stats["total_runs"]) * 100
                    interval = self.requester_intervals.get(requester_name, self.global_interval)
                    logger.info(f"   {requester_name}:")
                    logger.info(f"     执行次数: {stats['total_runs']}, 成功: {stats['successful_runs']}, 失败: {stats['failed_runs']}")
                    logger.info(f"     成功率: {success_rate:.1f}%, 间隔: {interval}秒")
        else:
            # 全局模式统计
            logger.info(f"   总执行次数: {self.total_runs}")
            logger.info(f"   成功次数: {self.successful_runs}")
            logger.info(f"   失败次数: {self.failed_runs}")
            if self.total_runs > 0:
                success_rate = (self.successful_runs / self.total_runs) * 100
                logger.info(f"   成功率: {success_rate:.1f}%")

    def get_status(self):
        """获取调度器状态信息"""
        if not self.is_running:
            return {"status": "stopped"}

        current_time = time.time()
        status = {
            "status": "running",
            "mode": "individual_intervals" if self.use_individual_intervals else "global_interval",
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "running_time": str(datetime.now() - self.start_time) if self.start_time else None,
            "requesters": {}
        }

        if self.use_individual_intervals:
            for requester_name in self.collector.api_requesters.keys():
                interval = self.requester_intervals.get(requester_name, self.global_interval)
                last_run = self.last_run_times.get(requester_name, 0)
                next_run = last_run + interval
                stats = self.requester_stats.get(requester_name, {})

                status["requesters"][requester_name] = {
                    "interval": interval,
                    "last_run": datetime.fromtimestamp(last_run).isoformat() if last_run > 0 else None,
                    "next_run": datetime.fromtimestamp(next_run).isoformat(),
                    "seconds_until_next": max(0, int(next_run - current_time)),
                    "stats": stats
                }

        return status

    def update_requester_interval(self, requester_name: str, new_interval: int):
        """更新指定请求器的执行间隔"""
        if requester_name not in self.collector.api_requesters:
            logger.error(f"❌ 请求器 {requester_name} 不存在")
            return False

        old_interval = self.requester_intervals.get(requester_name, self.global_interval)
        self.requester_intervals[requester_name] = new_interval

        logger.info(f"⚙️ 更新请求器间隔: {requester_name}")
        logger.info(f"   旧间隔: {old_interval}秒")
        logger.info(f"   新间隔: {new_interval}秒")

        return True

    def _check_and_execute_jobs(self):
        """检查并执行需要运行的请求器（独立间隔模式）"""
        current_time = time.time()
        ready_requesters = []

        # 检查哪些请求器需要执行
        for requester_name in self.collector.api_requesters.keys():
            interval = self.requester_intervals.get(requester_name, self.global_interval)
            last_run = self.last_run_times.get(requester_name, 0)

            if current_time - last_run >= interval:
                ready_requesters.append(requester_name)

        if ready_requesters:
            logger.info(f"🔄 检查到 {len(ready_requesters)} 个请求器需要执行: {', '.join(ready_requesters)}")
            self._execute_selected_jobs(ready_requesters)
        else:
            # 显示下次最近的执行时间
            next_times = []
            for requester_name in self.collector.api_requesters.keys():
                interval = self.requester_intervals.get(requester_name, self.global_interval)
                last_run = self.last_run_times.get(requester_name, 0)
                next_run = last_run + interval
                next_times.append((requester_name, next_run))

            if next_times:
                next_times.sort(key=lambda x: x[1])
                next_requester, next_time = next_times[0]
                next_datetime = datetime.fromtimestamp(next_time)
                logger.debug(f"⏰ 下个执行: {next_requester} 在 {next_datetime.strftime('%H:%M:%S')}")

    def _execute_all_jobs(self):
        """执行所有请求器（全局间隔模式）"""
        if self.current_job_count >= self.max_concurrent_jobs:
            logger.warning(f"⚠️ 达到最大并发数限制 ({self.max_concurrent_jobs})，跳过本次执行")
            return

        self.current_job_count += 1
        self.total_runs += 1

        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logger.info(f"🔄 开始第 {self.total_runs} 次全局数据收集 ({current_time})")

            # 执行所有API请求器
            results = self.collector.run_all_concurrent()

            # 统计结果
            successful = sum(1 for r in results if r["success"])
            total = len(results)

            if successful == total:
                self.successful_runs += 1
                logger.info(f"✅ 第 {self.total_runs} 次执行完成: 全部成功 ({successful}/{total})")
            else:
                if successful > 0:
                    self.successful_runs += 1
                else:
                    self.failed_runs += 1

                logger.warning(f"⚠️ 第 {self.total_runs} 次执行完成: 部分成功 ({successful}/{total})")

                # 记录失败的请求器
                failed_requesters = [r["requester"] for r in results if not r["success"]]
                if failed_requesters:
                    logger.warning(f"   失败的请求器: {', '.join(failed_requesters)}")

            # 显示下次执行时间
            next_run = datetime.now() + timedelta(seconds=self.global_interval)
            logger.info(f"⏰ 下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            self.failed_runs += 1
            logger.error(f"❌ 第 {self.total_runs} 次执行失败: {str(e)}")
        finally:
            self.current_job_count -= 1

    def _execute_individual_jobs(self):
        """执行所有请求器（独立间隔模式启动时）"""
        all_requesters = list(self.collector.api_requesters.keys())
        logger.info(f"🔄 启动时执行所有 {len(all_requesters)} 个请求器")
        self._execute_selected_jobs(all_requesters)

    def _execute_selected_jobs(self, requester_names):
        """执行指定的请求器"""
        if self.current_job_count >= self.max_concurrent_jobs:
            logger.warning(f"⚠️ 达到最大并发数限制 ({self.max_concurrent_jobs})，跳过本次执行")
            return

        self.current_job_count += 1
        current_time = time.time()

        try:
            current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logger.info(f"🚀 执行 {len(requester_names)} 个请求器 ({current_time_str})")

            # 执行指定的API请求器
            results = self.collector.run_selected(requester_names)

            # 更新最后执行时间和统计信息
            for result in results:
                requester_name = result["requester"]
                self.last_run_times[requester_name] = current_time

                # 更新统计信息
                stats = self.requester_stats[requester_name]
                stats["total_runs"] += 1
                stats["last_run_time"] = current_time_str

                if result["success"]:
                    stats["successful_runs"] += 1
                else:
                    stats["failed_runs"] += 1

                # 计算下次执行时间
                interval = self.requester_intervals.get(requester_name, self.global_interval)
                next_run_time = current_time + interval
                stats["next_run_time"] = datetime.fromtimestamp(next_run_time).strftime("%Y-%m-%d %H:%M:%S")

            # 统计结果
            successful = sum(1 for r in results if r["success"])
            total = len(results)

            if successful == total:
                logger.info(f"✅ 执行完成: 全部成功 ({successful}/{total})")
            else:
                logger.warning(f"⚠️ 执行完成: 部分成功 ({successful}/{total})")

                # 记录失败的请求器
                failed_requesters = [r["requester"] for r in results if not r["success"]]
                if failed_requesters:
                    logger.warning(f"   失败的请求器: {', '.join(failed_requesters)}")

            # 显示各请求器的下次执行时间
            if len(requester_names) <= 5:  # 只有少数请求器时显示详细信息
                for requester_name in requester_names:
                    stats = self.requester_stats[requester_name]
                    logger.info(f"   {requester_name}: 下次执行 {stats['next_run_time']}")

        except Exception as e:
            logger.error(f"❌ 执行失败: {str(e)}")
        finally:
            self.current_job_count -= 1

# ========== 主程序类 ==========

class APIDataCollector:
    """API数据收集器主类"""
    
    def __init__(self):
        """初始化数据收集器"""
        self.file_manager = FileManager()
        self.api_requesters = self._discover_api_requesters()
        
        # 确保数据存储目录存在
        ensure_data_storage_directories()
        
        logger.info(f"✅ API数据收集器初始化完成，发现 {len(self.api_requesters)} 个API请求器")
    
    def _discover_api_requesters(self) -> Dict[str, str]:
        """
        自动发现api_requests目录中的所有请求器脚本（支持多级子文件夹结构）

        Returns:
            请求器名称到文件路径的映射
        """
        requesters = {}
        api_requests_dir = project_root / "api_requests"

        if not api_requests_dir.exists():
            logger.warning("⚠️ api_requests目录不存在")
            return requesters

        # 递归扫描所有子文件夹中的Python文件
        def scan_directory(directory: Path, depth: int = 0):
            """递归扫描目录"""
            if depth > 3:  # 限制递归深度，避免无限递归
                return

            for item in directory.iterdir():
                if item.is_dir() and not item.name.startswith('.') and item.name != "__pycache__":
                    # 递归扫描子目录
                    scan_directory(item, depth + 1)
                elif item.is_file() and item.suffix == ".py" and item.name != "__init__.py":
                    # 找到Python文件
                    requester_name = item.stem

                    # 避免重复添加（如果有同名文件）
                    if requester_name not in requesters:
                        requesters[requester_name] = str(item)

                        # 构建相对路径用于显示
                        relative_path = item.relative_to(api_requests_dir)
                        logger.info(f"🔍 发现API请求器: {requester_name} ({relative_path.parent})")
                    else:
                        # 如果有重名，显示警告
                        existing_path = Path(requesters[requester_name]).relative_to(api_requests_dir)
                        new_path = item.relative_to(api_requests_dir)
                        logger.warning(f"⚠️ 发现重名请求器: {requester_name}")
                        logger.warning(f"   已存在: {existing_path}")
                        logger.warning(f"   跳过: {new_path}")

        # 开始扫描
        scan_directory(api_requests_dir)

        logger.info(f"📊 总共发现 {len(requesters)} 个API请求器")
        return requesters
    
    def run_single_requester(self, requester_name: str) -> Dict[str, Any]:
        """
        运行单个API请求器
        
        Args:
            requester_name: 请求器名称
            
        Returns:
            执行结果
        """
        start_time = time.time()
        result = {
            "requester": requester_name,
            "success": False,
            "message": "",
            "execution_time": 0,
            "error": None
        }
        
        try:
            if requester_name not in self.api_requesters:
                result["message"] = f"请求器 {requester_name} 不存在"
                return result
            
            script_path = self.api_requesters[requester_name]
            logger.info(f"🚀 启动API请求器: {requester_name}")

            # 使用subprocess运行请求器脚本
            import subprocess

            # 为Google AI请求器添加--auto参数
            cmd = [sys.executable, script_path]
            if requester_name == "google_ai_generate_gemini_content":
                cmd.append("--auto")

            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            execution_time = time.time() - start_time
            result["execution_time"] = execution_time
            
            if process.returncode == 0:
                result["success"] = True
                result["message"] = f"执行成功 (耗时: {execution_time:.2f}s)"
                logger.info(f"✅ {requester_name} 执行成功 (耗时: {execution_time:.2f}s)")
            else:
                result["message"] = f"执行失败: {process.stderr}"
                logger.error(f"❌ {requester_name} 执行失败: {process.stderr}")
            
        except subprocess.TimeoutExpired:
            result["message"] = "执行超时"
            result["error"] = "timeout"
            logger.error(f"⏰ {requester_name} 执行超时")
        except Exception as e:
            result["message"] = f"执行异常: {str(e)}"
            result["error"] = str(e)
            logger.error(f"❌ {requester_name} 执行异常: {str(e)}")
        
        return result
    
    def run_all_concurrent(self, max_workers: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        并发运行所有API请求器
        
        Args:
            max_workers: 最大并发工作进程数，默认为CPU核心数
            
        Returns:
            所有请求器的执行结果列表
        """
        if not self.api_requesters:
            logger.warning("⚠️ 没有发现任何API请求器")
            return []
        
        # 设置默认并发数
        if max_workers is None:
            max_workers = min(len(self.api_requesters), multiprocessing.cpu_count())
        
        logger.info(f"🚀 开始并发执行 {len(self.api_requesters)} 个API请求器 (并发数: {max_workers})")
        
        results = []
        start_time = time.time()
        
        # 使用进程池并发执行
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_requester = {
                executor.submit(run_requester_process, requester_name, script_path): requester_name
                for requester_name, script_path in self.api_requesters.items()
            }
            
            # 收集结果
            for future in as_completed(future_to_requester):
                requester_name = future_to_requester[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result["success"]:
                        logger.info(f"✅ {requester_name} 完成")
                    else:
                        logger.error(f"❌ {requester_name} 失败: {result['message']}")
                        
                except Exception as e:
                    error_result = {
                        "requester": requester_name,
                        "success": False,
                        "message": f"进程执行异常: {str(e)}",
                        "execution_time": 0,
                        "error": str(e)
                    }
                    results.append(error_result)
                    logger.error(f"❌ {requester_name} 进程异常: {str(e)}")
        
        total_time = time.time() - start_time
        
        # 统计结果
        successful = sum(1 for r in results if r["success"])
        failed = len(results) - successful
        
        logger.info(f"🎯 并发执行完成:")
        logger.info(f"   总耗时: {total_time:.2f}s")
        logger.info(f"   成功: {successful}/{len(results)}")
        logger.info(f"   失败: {failed}/{len(results)}")
        
        return results
    
    def run_selected(self, requester_names: List[str], max_workers: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        运行选定的API请求器
        
        Args:
            requester_names: 要运行的请求器名称列表
            max_workers: 最大并发工作进程数
            
        Returns:
            执行结果列表
        """
        # 验证请求器名称
        valid_requesters = {}
        invalid_requesters = []
        
        for name in requester_names:
            if name in self.api_requesters:
                valid_requesters[name] = self.api_requesters[name]
            else:
                invalid_requesters.append(name)
        
        if invalid_requesters:
            logger.warning(f"⚠️ 以下请求器不存在: {', '.join(invalid_requesters)}")
        
        if not valid_requesters:
            logger.error("❌ 没有有效的请求器可执行")
            return []
        
        # 临时替换请求器列表
        original_requesters = self.api_requesters
        self.api_requesters = valid_requesters
        
        try:
            return self.run_all_concurrent(max_workers)
        finally:
            self.api_requesters = original_requesters
    
    def list_requesters(self):
        """列出所有可用的API请求器"""
        logger.info("📋 可用的API请求器:")
        for i, (name, path) in enumerate(self.api_requesters.items(), 1):
            logger.info(f"   {i:2d}. {name}")
        
        return list(self.api_requesters.keys())
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取数据存储统计信息"""
        return self.file_manager.get_storage_stats()

def run_requester_process(requester_name: str, script_path: str) -> Dict[str, Any]:
    """
    在独立进程中运行API请求器
    这个函数需要在模块级别定义以支持multiprocessing
    """
    import subprocess
    import time
    
    start_time = time.time()
    result = {
        "requester": requester_name,
        "success": False,
        "message": "",
        "execution_time": 0,
        "error": None
    }
    
    try:
        process = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        execution_time = time.time() - start_time
        result["execution_time"] = execution_time
        
        if process.returncode == 0:
            result["success"] = True
            result["message"] = f"执行成功 (耗时: {execution_time:.2f}s)"
        else:
            result["message"] = f"执行失败: {process.stderr}"
        
    except subprocess.TimeoutExpired:
        result["message"] = "执行超时"
        result["error"] = "timeout"
    except Exception as e:
        result["message"] = f"执行异常: {str(e)}"
        result["error"] = str(e)
    
    return result

def main():
    """主函数 - 支持调度器模式和手动模式"""
    parser = argparse.ArgumentParser(description='API数据收集器 - 支持独立间隔管理和调度器模式')
    parser.add_argument('--list', action='store_true', help='仅列出所有可用的API请求器')
    parser.add_argument('--run', nargs='*', help='运行指定的API请求器（不指定则运行全部）')
    parser.add_argument('--workers', type=int, help='最大并发工作进程数')
    parser.add_argument('--stats', action='store_true', help='仅显示数据存储统计信息')
    parser.add_argument('--manual', action='store_true', help='手动模式，执行一次后退出')
    parser.add_argument('--interval', type=int, help='全局调度器执行间隔（秒），覆盖配置文件设置')
    parser.add_argument('--no-scheduler', action='store_true', help='禁用调度器，仅执行一次')
    parser.add_argument('--no-individual', action='store_true', help='禁用独立间隔管理，使用全局间隔')
    parser.add_argument('--set-interval', nargs=2, metavar=('REQUESTER', 'SECONDS'),
                       help='设置指定请求器的执行间隔，格式: --set-interval requester_name seconds')
    parser.add_argument('--show-intervals', action='store_true', help='显示所有请求器的执行间隔配置')
    parser.add_argument('--status', action='store_true', help='显示调度器运行状态（需要调度器正在运行）')

    args = parser.parse_args()

    # 创建数据收集器
    collector = APIDataCollector()

    try:
        if args.list:
            # 仅列出所有请求器
            collector.list_requesters()
            return

        elif args.stats:
            # 仅显示存储统计信息
            stats = collector.get_storage_stats()
            logger.info("📊 数据存储统计信息:")
            logger.info(f"   API总数: {stats.get('total_apis', 0)}")
            logger.info(f"   文件总数: {stats.get('total_files', 0)}")
            logger.info(f"   总大小: {stats.get('total_size_bytes', 0) / 1024 / 1024:.2f} MB")
            return

        elif args.show_intervals:
            # 显示所有请求器的执行间隔配置
            logger.info("📋 请求器执行间隔配置:")
            logger.info(f"   全局默认间隔: {SCHEDULER_CONFIG['interval_seconds']}秒")
            logger.info(f"   独立间隔管理: {'启用' if SCHEDULER_CONFIG['use_individual_intervals'] else '禁用'}")
            logger.info("   各请求器配置:")

            for requester_name in collector.api_requesters.keys():
                interval = API_REQUESTER_INTERVALS.get(requester_name, SCHEDULER_CONFIG['interval_seconds'])
                if interval >= 3600:
                    interval_str = f"{interval//3600}小时"
                elif interval >= 60:
                    interval_str = f"{interval//60}分钟"
                else:
                    interval_str = f"{interval}秒"
                logger.info(f"     {requester_name}: {interval}秒 ({interval_str})")
            return

        elif args.set_interval:
            # 设置指定请求器的执行间隔
            requester_name, interval_str = args.set_interval
            try:
                new_interval = int(interval_str)
                if new_interval <= 0:
                    logger.error("❌ 执行间隔必须大于0秒")
                    return

                if requester_name not in collector.api_requesters:
                    logger.error(f"❌ 请求器 '{requester_name}' 不存在")
                    logger.info("💡 使用 --list 查看所有可用的请求器")
                    return

                # 这里只是显示设置，实际的间隔更新需要在调度器运行时进行
                old_interval = API_REQUESTER_INTERVALS.get(requester_name, SCHEDULER_CONFIG['interval_seconds'])
                logger.info(f"⚙️ 请求器间隔设置:")
                logger.info(f"   请求器: {requester_name}")
                logger.info(f"   当前间隔: {old_interval}秒")
                logger.info(f"   新间隔: {new_interval}秒")
                logger.info("💡 注意: 此设置仅在当前会话有效，要永久保存请修改config.py文件")

                # 临时更新配置（仅当前会话）
                API_REQUESTER_INTERVALS[requester_name] = new_interval
                logger.info("✅ 间隔设置已更新（当前会话）")
                return

            except ValueError:
                logger.error("❌ 间隔时间必须是有效的整数（秒）")
                return

        elif args.manual or args.no_scheduler:
            # 手动模式或禁用调度器，执行一次后退出
            mode_name = "手动模式" if args.manual else "单次执行模式"
            logger.info(f"🎯 启动{mode_name}...")

            if args.run is not None:
                # 运行指定的请求器
                if args.run:
                    logger.info(f"🎯 运行指定的API请求器: {', '.join(args.run)}")
                    results = collector.run_selected(args.run, args.workers)
                else:
                    logger.info("🔄 运行所有API请求器...")
                    results = collector.run_all_concurrent(args.workers)
            else:
                # 默认运行所有请求器
                logger.info("🔄 运行所有API请求器...")
                results = collector.run_all_concurrent(args.workers)

            # 输出结果摘要
            successful = sum(1 for r in results if r["success"])
            total = len(results)

            print(f"\n🎯 执行结果摘要:")
            print(f"   成功: {successful}/{total}")
            print(f"   失败: {total - successful}/{total}")

            if total - successful > 0:
                print(f"\n❌ 失败的请求器:")
                for result in results:
                    if not result["success"]:
                        print(f"   - {result['requester']}: {result['message']}")

            if successful == total:
                print(f"\n✅ 所有API请求器执行成功！")
            else:
                print(f"\n⚠️ 有 {total - successful} 个请求器执行失败")
            return

        # 默认行为：启动调度器模式
        if not SCHEDULER_CONFIG["enable_scheduler"]:
            logger.warning("⚠️ 调度器在配置中被禁用，切换到单次执行模式")
            logger.info("🔄 运行所有API请求器...")
            results = collector.run_all_concurrent(args.workers)

            # 输出结果摘要
            successful = sum(1 for r in results if r["success"])
            total = len(results)
            print(f"\n🎯 执行结果摘要: 成功 {successful}/{total}")
            return

        # 启动调度器
        logger.info("🚀 启动调度器模式...")
        logger.info("💡 提示: 使用 --manual 参数进入手动模式")
        logger.info("💡 提示: 使用 --no-scheduler 参数禁用调度器")
        logger.info("💡 提示: 使用 --show-intervals 查看间隔配置")

        scheduler = APIScheduler(collector)

        # 应用命令行参数覆盖配置
        if args.no_individual:
            scheduler.use_individual_intervals = False
            logger.info("⚙️ 已禁用独立间隔管理，使用全局间隔模式")

        if args.interval:
            scheduler.global_interval = args.interval
            logger.info(f"⚙️ 全局执行间隔已设置为: {args.interval}秒")

        scheduler.start()

    except KeyboardInterrupt:
        logger.info("⚡ 用户中断执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 