#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoinGlass RSI列表数据请求器
获取多种加密货币在不同时间周期下的相对强弱指数（RSI）数值
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import COINGLASS_CONFIG

class CoinglassRSIListRequester:
    """CoinGlass RSI列表数据请求器"""

    def __init__(self):
        """初始化请求器"""
        self.api_identifier = "coinglass_get_rsi_list"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        
        # 创建HTTP客户端
        self.http_client = HTTPClient(
            timeout=COINGLASS_CONFIG["timeout"],
            max_retries=COINGLASS_CONFIG["max_retries"]
        )
        
        # 设置CoinGlass API认证头
        self.headers = {
            "accept": "application/json",
            "CG-API-KEY": COINGLASS_CONFIG["api_key"]
        }
        
        # API配置
        self.base_url = COINGLASS_CONFIG["base_url"]
        self.endpoint = "/api/futures/rsi/list"

    def get_rsi_data(self):
        """
        获取RSI列表数据
        
        Returns:
            bool: 是否成功获取数据
        """
        try:
            self.logger.info(f"🚀 开始获取CoinGlass RSI列表数据...")
            
            # 构建请求URL
            url = f"{self.base_url}{self.endpoint}"
            
            # 发送请求
            success, response, message = self.http_client.get(
                url=url,
                headers=self.headers
            )
            
            if not success or not response:
                self.logger.error(f"❌ 获取数据失败：{message}")
                return False
            
            # 验证响应格式
            if not isinstance(response, dict) or response.get("code") != "0":
                error_msg = response.get("msg", "未知错误") if isinstance(response, dict) else "响应格式错误"
                self.logger.error(f"❌ API返回错误: {error_msg}")
                return False
            
            # 提取数据
            data_list = response.get("data", [])
            if not data_list:
                self.logger.warning("⚠️ 返回的数据为空")
                return False
            
            # 计算统计信息
            total_symbols = len(data_list)
            
            # 构建元数据
            metadata = {
                "request_url": url,
                "symbols_count": total_symbols,
                "data_type": "rsi_indicators",
                "time_periods": ["15m", "1h", "4h", "12h", "24h", "1w"],
                "update_frequency": "10秒一次",
                "api_level_required": "标准版及以上"
            }
            
            # 保存数据
            save_success, file_path, save_message = self.file_manager.save_api_data(
                api_identifier=self.api_identifier,
                data=response,
                metadata=metadata
            )
            
            if save_success:
                self.logger.info(f"✅ 成功获取并保存 {total_symbols} 个币种的RSI数据")
                return True
            else:
                self.logger.error(f"❌ 数据保存失败: {save_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 获取RSI数据时发生异常: {str(e)}")
            return False

def main():
    """主函数"""
    requester = CoinglassRSIListRequester()
    success = requester.get_rsi_data()
    
    if success:
        print("CoinGlass RSI列表数据获取成功")
    else:
        print("CoinGlass RSI列表数据获取失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
