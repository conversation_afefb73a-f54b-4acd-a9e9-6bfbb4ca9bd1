# 全市场请求器包
# All Market API Requesters

from .binance import *
from .coinglass import *

# 导入所有全市场请求器
from .binance import (
    BinanceFuturesDataManager,
    BinanceFuturesExchangeInfoRequester,
    BinanceFundingInfoRequester,
    BinanceFuturesPremiumIndexRequester,
    BinanceIndexInfoRequester,
    BinanceAssetIndexRequester,
    BinanceConstituentsRequester,
    BinanceTickerPriceRequester,
    BinanceBookTickerRequester
)

from .coinglass import (
    CoinglassV4FuturesAllMarketsRequester,
    CoinglassV4FuturesBinanceMarketsRequester,
    CoinglassV4SpotAllMarketsRequester,
    CoinglassFearGreedIndexRequester,
    CoinglassBitcoinS2FRequester,
    CoinglassCDRIIndexRequester,
    CoinglassCGDIIndexRequester,
    CoinglassGoldenRatioRequester,
    CoinglassRSIListRequester
)

__all__ = [
    # Binance 全市场请求器
    'BinanceFuturesDataManager',
    'BinanceFuturesExchangeInfoRequester',
    'BinanceFundingInfoRequester',
    'BinanceFuturesPremiumIndexRequester',
    'BinanceIndexInfoRequester',
    'BinanceAssetIndexRequester',
    'BinanceConstituentsRequester',
    'BinanceTickerPriceRequester',
    'BinanceBookTickerRequester',

    # CoinGlass 全市场请求器
    'CoinglassV4FuturesAllMarketsRequester',
    'CoinglassV4FuturesBinanceMarketsRequester',
    'CoinglassV4SpotAllMarketsRequester',
    'CoinglassFearGreedIndexRequester',
    'CoinglassBitcoinS2FRequester',
    'CoinglassCDRIIndexRequester',
    'CoinglassCGDIIndexRequester',
    'CoinglassGoldenRatioRequester',
    'CoinglassRSIListRequester'
]
