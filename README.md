# 🚀 加密货币数据收集与分析平台

一个全面的加密货币数据收集和分析平台，集成多个主流数据源，提供统一的数据接口和智能分析功能。

## 📋 项目概览

- **🎯 目标**: 构建统一的加密货币数据收集和分析平台
- **📊 数据源**: Binance、CoinGlass、Google AI (共27个API端点)
- **🔧 功能**: 实时数据收集、历史数据分析、AI辅助决策
- **💾 存储**: 智能文件管理、按币种分类存储、自动清理

## 🔧 核心功能

### 数据收集 (27个API端点)
- **Binance期货** (15个): K线、资金费率、持仓量、价格指数
- **CoinGlass市场** (11个): 期权、基差、情绪指标、技术分析
- **Google AI** (1个): 智能内容生成

### 数据管理
- ✅ 按时间戳自动命名和组织
- ✅ 按币种分类存储 (期权数据)
- ✅ 数据新鲜度检查
- ✅ 可配置的自动清理
- ✅ 完整的元数据收集

### 错误处理
- ✅ 智能重试机制
- ✅ 全面的日志记录
- ✅ 异常恢复策略
- ✅ 性能监控统计

## 📚 完整文档

| 文档 | 描述 | 适用对象 |
|------|------|----------|
| **[📖 项目总览](PROJECT_OVERVIEW.md)** | 项目全貌、功能特性、性能指标 | 所有用户 |
| **[🏗️ 项目架构](PROJECT_ARCHITECTURE.md)** | 详细的项目结构和模块说明 | 开发者、架构师 |
| **[🔌 API文档](API_DOCUMENTATION.md)** | 完整的API接口使用指南 | 开发者 |
| **[👤 用户指南](USER_GUIDE.md)** | 从入门到高级的使用教程 | 最终用户 |
| **[⚙️ 配置指南](CONFIGURATION_GUIDE.md)** | 详细的配置说明和最佳实践 | 系统管理员 |

## 📊 项目统计

```
📁 总文件数: 80+ Python文件
🔌 API端点: 27个不同端点
💾 数据源: 3个主要数据源
🪙 支持币种: 所有主流加密货币
📈 功能覆盖: K线、资金费率、持仓量、期权、情绪指标、AI分析
```

## 🏗️ 项目结构

```
📦 Project/
├── 📁 api_requests/              # 🔌 API请求模块
│   ├── 📁 binance/              # Binance API (15个端点)
│   ├── 📁 coinglass/            # CoinGlass API (11个端点)
│   └── 📁 google_ai/            # Google AI API (1个端点)
├── 📁 crypto_project/           # 🤖 交易分析项目
├── 📁 data_storage/             # 💾 数据存储 (按API分类)
├── 📁 logs/                     # 📋 日志文件
├── 📄 main.py                   # 🛠️ 核心工具类
├── 📄 config.py                 # ⚙️ 配置文件
└── 📚 文档文件
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 配置API密钥 (编辑 config.py)
COINGLASS_CONFIG["api_key"] = "your_api_key_here"
GOOGLE_AI_CONFIG["api_key"] = "your_api_key_here"
```

### 2. 基础使用
```python
# Binance期货数据
from api_requests.binance.binance_futures_data_manager import BinanceFuturesDataManager
manager = BinanceFuturesDataManager()
success = manager.fetch_continuous_klines("BTCUSDT", "PERPETUAL", "1h", 500)

# CoinGlass期权数据
from api_requests.coinglass.coinglass_get_option_info import CoinglassOptionInfoRequester
requester = CoinglassOptionInfoRequester()
success = requester.fetch_data("BTC")
```

### 3. 验证功能
```bash
# 运行测试脚本
python test_complete_functionality.py
```

## 🎯 使用场景

### 📈 量化交易
- 实时价格和资金费率监控
- 持仓量变化分析
- 多空比例统计

### 📊 市场分析
- 期权市场数据分析
- 市场情绪指标追踪
- 技术指标计算

### 🤖 AI辅助
- 智能市场分析
- 自动报告生成
- 趋势预测

## 💡 高级功能示例

### 批量数据获取
```python
# Binance批量获取
symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
results = manager.fetch_multiple_symbols_data(symbols, interval="1h", limit=100)

for symbol, api_results in results.items():
    success_count = sum(1 for success in api_results.values() if success)
    total_count = len(api_results)
    print(f"{symbol}: {success_count}/{total_count} APIs成功")

# CoinGlass批量获取
crypto_symbols = ["BTC", "ETH", "SOL"]
results = requester.fetch_multiple_symbols(crypto_symbols)

for symbol, success in results.items():
    status = "✅ 成功" if success else "❌ 失败"
    print(f"{symbol}: {status}")
```

### 数据分析示例
```python
# 分析BTC期权市场数据
data = requester.get_latest_data("BTC")
if data and 'data' in data and 'data' in data['data']:
    option_data = data['data']['data']

    # 统计分析
    total_volume_usd = 0
    total_oi_usd = 0
    exchanges = []

    for record in option_data:
        if record.get('exchange_name') != 'All':
            exchanges.append(record.get('exchange_name'))
            total_volume_usd += record.get('volume_usd_24h', 0)
            total_oi_usd += record.get('open_interest_usd', 0)

    print(f"📈 BTC期权市场分析:")
    print(f"   交易所数量: {len(exchanges)}")
    print(f"   24h成交量: ${total_volume_usd:,.0f}")
    print(f"   总持仓量: ${total_oi_usd:,.0f}")
```

## 📊 API端点概览

### Binance API (15个端点)
| 类别 | 端点数 | 主要功能 |
|------|--------|----------|
| K线数据 | 4个 | 连续合约、价格指数、标记价格、溢价指数K线 |
| 资金费率 | 2个 | 资金费率历史和当前信息 |
| 持仓量 | 4个 | 持仓量历史、大户比率、全球多空比 |
| 价格数据 | 2个 | 合约价格、最优挂单 |
| 指数数据 | 3个 | 综合指数、多资产汇率、指数成分 |

### CoinGlass API (11个端点)
| 类别 | 端点数 | 主要功能 |
|------|--------|----------|
| 期权数据 | 1个 | 各交易所期权市场数据 |
| 基差数据 | 1个 | 期货现货价差历史 |
| 情绪指标 | 4个 | 恐慌贪婪指数、CDRI、CGDI、黄金比例 |
| 技术指标 | 2个 | 比特币S2F模型、RSI列表 |
| 市场数据 | 3个 | 期货全市场、Binance期货、现货全市场 |

### Google AI API (1个端点)
| 类别 | 端点数 | 主要功能 |
|------|--------|----------|
| AI生成 | 1个 | Gemini智能内容生成和分析 |

## ⚙️ 配置要点

### API密钥配置
```python
# config.py 中的关键配置
COINGLASS_CONFIG = {
    "api_key": "your_coinglass_api_key_here",  # 必需
    "base_url": "https://open-api.coinglass.com"
}

GOOGLE_AI_CONFIG = {
    "api_key": "your_google_ai_api_key_here",  # 可选
    "default_model": "gemini-pro"
}
```

### 数据存储配置
```python
FILE_MANAGER_CONFIG = {
    "storage_root": "data_storage",
    "auto_cleanup": False,  # 永久保留数据
    "retention_days": 7,    # 如启用清理
    "max_files_per_api": 1000
}
```

## 🔍 数据存储结构

### 文件组织
```
data_storage/
├── binance/
│   ├── binance_continuous_klines_BTCUSDT_PERPETUAL_1h/
│   ├── binance_funding_rate_BTCUSDT/
│   └── ...
├── coinglass/
│   ├── coinglass_get_option_info_BTC/
│   ├── coinglass_get_option_info_ETH/
│   └── ...
└── google_ai/
    └── google_ai_generate_gemini_content/
```

### 数据格式
每个JSON文件包含：
- **timestamp**: 数据收集时间
- **api_identifier**: API标识符
- **metadata**: 请求元数据和统计信息
- **data**: 原始API响应数据

## 🛠️ 开发指南

### 添加新API端点
1. 在相应目录创建Python文件
2. 继承标准请求器模式
3. 配置API参数和错误处理
4. 测试并验证功能

### 最佳实践
- 使用统一的错误处理机制
- 实现适当的重试策略
- 添加详细的日志记录
- 遵循现有的代码结构

## 🔄 最新更新

### ✅ 已完成功能
- [x] 完整的Binance期货API集成 (15个端点)
- [x] 完整的CoinGlass API集成 (11个端点)
- [x] 期权数据按币种分类存储
- [x] 批量数据处理功能
- [x] 完善的错误处理和日志记录
- [x] 详细的文档和使用示例

### 🔄 进行中
- [ ] 每个API端点拆分为独立文件
- [ ] Web管理界面
- [ ] 数据可视化仪表板

## 🤝 贡献

欢迎贡献代码、报告问题或改进文档！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📞 支持

- 📖 **文档**: 查看上方的完整文档链接
- 🐛 **问题**: 使用 GitHub Issues 报告问题
- 💡 **建议**: 欢迎提出新功能建议

---

**开始探索**: 从 [📖 项目总览](PROJECT_OVERVIEW.md) 开始了解项目全貌，或直接查看 [👤 用户指南](USER_GUIDE.md) 快速上手！
